package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.registerservice.statistics.inspections_statistics.InspectionsStatistics;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "default")
public interface InspectionsStatisticsMapper {

    InspectionsStatisticsMapper INSTANCE = Mappers.getMapper(InspectionsStatisticsMapper.class);

    List<org.openapitools.model.InspectionsStatistics> inspectionsStatisticsListToApiInspectionsStatisticsList(List<InspectionsStatistics> inspectionsStatisticsList);
}
