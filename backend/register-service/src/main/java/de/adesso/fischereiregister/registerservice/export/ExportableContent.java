package de.adesso.fischereiregister.registerservice.export;

import java.util.Arrays;

/**
 * Represents rendered content ready for export, encapsulating both metadata and binary data.
 **/
public record ExportableContent(
        String filename,
        RenderedContentType type,
        byte[] content
) {
    public String getFullFilename() {
        return String.join(".", filename, type.getExtension());
    }

    // Override equals, hashCode, and toString to properly handle the byte array field,
    // since Java arrays use reference-based equality and do not override these methods
    // for content comparison or hashing.

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof ExportableContent other)) return false;
        return filename.equals(other.filename)
                && type == other.type
                && Arrays.equals(content, other.content);
    }

    @Override
    public int hashCode() {
        return 31 * (filename.hashCode() + type.hashCode()) + Arrays.hashCode(content);
    }

    @Override
    public String toString() {
        return "ExportableContent{" +
                "filename='" + filename + '\'' +
                ", type=" + type +
                ", content=" + Arrays.toString(content) +
                '}';
    }
}
