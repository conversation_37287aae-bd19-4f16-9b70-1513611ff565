package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.ban.services.BanViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class BansStatisticsServiceImpl implements BansStatisticsService {

    private final BanViewService banViewService;

    @Override
    public Integer getActiveBansAmount() {
        try {
            log.debug("Fetching total active bans count");

            Integer activeBansCount = banViewService.getActiveBansAmount();

            log.debug("Successfully fetched total active bans count: {}", activeBansCount);

            return activeBansCount;
        } catch (Exception e) {
            log.error("Error fetching total active bans count: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch total active bans count", e);
        }
    }

    @Override
    public Integer getActiveBansAmountByFederalState(String federalState) {
        try {
            log.debug("Fetching active bans count for federal state: {}", federalState);

            Integer activeBansCount = banViewService.getActiveBansAmountByFederalState(federalState);

            log.debug("Successfully fetched active bans count for federal state: {}", activeBansCount);

            return activeBansCount;
        } catch (Exception e) {
            log.error("Error fetching active bans count for federal state: {}: {}", federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch active bans count for federal state " + federalState, e);
        }
    }

    @Override
    public List<BansStatistics> getBansStatistics(List<Integer> years, String federalState) {
        try {
            log.debug("Fetching ban statistics for federal state: {} and years: {}", federalState, years);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : banViewService.getAvailableYears();

            List<BansStatistics> result = new ArrayList<>();

            for (Integer year : yearsToQuery) {
                // Get counts directly from BanViewService for database-level operations
                Integer issuedCount;
                if (federalState != null && !federalState.isEmpty()) {
                    issuedCount = banViewService.getIssuedAmountByFederalStateAndYear(federalState, year);
                } else {
                    issuedCount = banViewService.getIssuedAmountByYear(year);
                }

                // Create data entry with issued, started, and expired counts
                BansStatisticsData data = new BansStatisticsData(issuedCount);

                // Create BansStatistics with the year and data entry
                BansStatistics bansStatistics = new BansStatistics(year, data);
                result.add(bansStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(BansStatistics::year).reversed());

            log.debug("Successfully fetched ban statistics for federal state: {} and years: {}, result size: {}",
                    federalState, years, result.size());

            return result;
        } catch (Exception e) {
            log.error("Error fetching ban statistics for federal state: {} and years: {}: {}",
                    federalState, years, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch ban statistics", e);
        }
    }

    @Override
    public List<Integer> getAvailableYears() {
        try {
            log.debug("Fetching available years for ban statistics");

            List<Integer> availableYears = banViewService.getAvailableYears();

            log.debug("Successfully fetched available years for ban statistics: {}", availableYears);

            return availableYears;
        } catch (Exception e) {
            log.error("Error fetching available years for ban statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available years", e);
        }
    }
}
