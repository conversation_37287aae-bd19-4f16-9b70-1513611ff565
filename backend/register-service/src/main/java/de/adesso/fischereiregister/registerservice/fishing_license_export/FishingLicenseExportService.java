package de.adesso.fischereiregister.registerservice.fishing_license_export;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.LimitedLicenseApproval;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.ValidityPeriod;
import de.adesso.fischereiregister.registerservice.export.ExportContent;

import java.util.UUID;


public interface FishingLicenseExportService {

    ExportContent exportFishingLicense(UUID registerId, String documentId);

    ExportContent exportFishingLicense(UUID registerId, String salt, Person person, IdentificationDocument document);

    ExportContent exportFishingTaxDocument(UUID registerId, String documentId);

    ExportContent exportFishingCertificate(String fishingCertificateId) throws RulesProcessingException;

    ExportContent exportFishingTaxDocument(UUID registerEntryId, String salt, Person person, IdentificationDocument document);

    ExportContent exportLimitedLicenseApproval(UUID registerEntryId, String documentId) throws RulesProcessingException;

    ExportContent exportLimitedLicenseApprovalPreview(Person person, ValidityPeriod validityPeriod, String federalState, LimitedLicenseApproval limitedLicenseApproval) throws RulesProcessingException;
}
