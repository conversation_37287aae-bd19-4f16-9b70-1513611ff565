package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class BansStatisticsExportServiceImpl implements BansStatisticsExportService {

    private static final String FILENAME_PREFIX = "bans-statistics";

    private final BanViewService banViewService;

    @Override
    public ExportContent exportBansStatistics(String federalState, List<Integer> years) {
        try {
            log.debug("Exporting bans statistics for federalState: {}, years: {}", federalState, years);

            final ExportContent exportContent = generateBansStatisticsCsv(federalState, years);

            log.info("Bans statistics exported: {}", exportContent.getFullFilename());

            return exportContent;

        } catch (Exception e) {
            log.error("Error exporting bans statistics for federalState {}, years {}: {}", federalState, years, e.getMessage(), e);
            throw new RuntimeException("Failed to export bans statistics", e);
        }
    }

    private ExportContent generateBansStatisticsCsv(String federalState, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Jahr", "Bundesland", "Neu verhängte Sperren", "Gestartete Sperren")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            if (federalState != null && !federalState.isEmpty()) {
                // Export for specific federal state
                for (Integer year : years) {
                    Integer issuedCount = banViewService.getIssuedAmountByFederalStateAndYear(federalState, year);
                    Integer startedCount = banViewService.getStartedAmountByFederalStateAndYear(federalState, year);
                    csvPrinter.printRecord(
                            year,
                            federalState,
                            issuedCount,
                            startedCount
                    );
                }
            } else {
                // Export for all federal states
                for (FederalState fs : FederalState.values()) {
                    for (Integer year : years) {
                        Integer issuedCount = banViewService.getIssuedAmountByFederalStateAndYear(fs.name(), year);
                        Integer startedCount = banViewService.getStartedAmountByFederalStateAndYear(fs.name(), year);
                        csvPrinter.printRecord(
                                year,
                                fs.name(),
                                issuedCount,
                                startedCount
                        );
                    }
                }
            }
        }

        String filename = buildFilename(federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(String federalState) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState.toLowerCase());
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
    }
}
