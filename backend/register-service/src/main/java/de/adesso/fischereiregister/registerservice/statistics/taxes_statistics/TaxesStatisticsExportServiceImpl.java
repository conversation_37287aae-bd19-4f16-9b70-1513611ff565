package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class TaxesStatisticsExportServiceImpl implements TaxesStatisticsExportService {

    private static final String FILENAME_PREFIX = "taxes-statistics";
    private static final String DATETIME_PATTERN = "yyyyMMdd-HHmmss";

    private final TaxesStatisticsViewService taxesStatisticsViewService;

    @Override
    public ExportContent exportTaxesStatistics(
            List<Integer> years,
            String office,
            String federalState) {
        try {
            log.debug("Exporting taxes statistics for years: {}, office: {}, federalState: {}",
                    years, office, federalState);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : taxesStatisticsViewService.getAvailableYears();

            final ExportContent exportContent = generateTaxesStatisticsCsv(federalState, office, yearsToQuery);

            log.info("Taxes statistics exported: {}", exportContent.getFullFilename());

            return exportContent;

        } catch (Exception e) {
            log.error("Error exporting taxes statistics for years {}, office {}, federalState {}: {}",
                    years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export taxes statistics", e);
        }
    }

    private ExportContent generateTaxesStatisticsCsv(String federalState, String office, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Bundesland", "Jahr", "Behörde", "Antragsweg", "Gültigkeitszeitraum", "Anzahl", "Betrag")
                .build();

        List<TaxesStatisticsView> taxesStatisticsViews;

        // if federalState is provided the office filter is ignored
        if (federalState != null && !federalState.isEmpty()) {
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years);
        } else if (office != null && !office.isEmpty()) {
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, years);
        } else {
            taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByYears(years);
        }

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            taxesStatisticsViews.stream()
                    .sorted(Comparator.comparing(TaxesStatisticsView::getYear).reversed())
                    .forEach(view -> {
                        try {
                            csvPrinter.printRecord(
                                    view.getFederalState(),
                                    view.getYear(),
                                    view.getOffice(),
                                    view.getSource().name(),
                                    view.getDuration(),
                                    view.getCount(),
                                    view.getRevenue()
                            );
                        } catch (IOException e) {
                            log.error("Error writing taxes statistics to CSV: {}", e.getMessage(), e);
                        }
                    });
        }

        String filename = buildFilename(office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(String office, String federalState) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState.toLowerCase());
        }

        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "-").toLowerCase());
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN));
    }
}
