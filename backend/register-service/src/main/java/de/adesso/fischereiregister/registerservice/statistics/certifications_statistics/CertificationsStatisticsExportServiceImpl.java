package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class CertificationsStatisticsExportServiceImpl implements CertificationsStatisticsExportService {

    private static final String FILENAME_PREFIX = "certifications-statistics";
    private static final String DATETIME_PATTERN = "yyyyMMdd-HHmmss";

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;

    @Override
    public ExportContent exportCertificationsStatistics(
            List<Integer> years,
            String office,
            String federalState) {
        try {
            log.debug("Exporting certifications statistics for years: {}, office: {}, federalState: {}",
                    years, office, federalState);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : certificationsStatisticsViewService.getAvailableYears();

            final ExportContent exportContent = generateCertificationsStatisticsCsv(federalState, office, yearsToQuery);

            log.info("certifications statistics exported: {}", exportContent.getFullFilename());

            return exportContent;
        } catch (Exception e) {
            log.error("Error exporting certifications statistics for years {}, office {}, federalState {}: {}",
                    years, office, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export certifications statistics", e);
        }
    }

    private ExportContent generateCertificationsStatisticsCsv(String federalState, String office, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Jahr", "Bundesland", "Prüfungsinstitution", "Anzahl ausgestellter Prüfungen")
                .build();

        List<CertificationsStatisticsView> certificationsStatisticsViews;

        // if federalState is provided the office filter is ignored
        if (federalState != null && !federalState.isEmpty()) {
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years);
        } else if (office != null && !office.isEmpty()) {
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByIssuerAndYears(office, years);
        } else {
            // if neither office nor federalState is provided get data for all federal states
            certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByYears(years);
        }

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            // Sort by key to ensure consistent ordering
            certificationsStatisticsViews.stream()
                    .sorted(Comparator.comparing(CertificationsStatisticsView::getYear).reversed())
                    .forEach(entry -> {
                        try {
                            csvPrinter.printRecord(
                                    entry.getYear(),
                                    entry.getFederalState(),
                                    entry.getIssuer(),
                                    entry.getCount()
                            );
                        } catch (IOException e) {
                            throw new RuntimeException("Error writing CSV record", e);
                        }
                    });
        }

        String filename = buildFilename(office, federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(String office, String federalState) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState.toLowerCase());
        }

        if (office != null && !office.isEmpty()) {
            filename.append("-").append(office.replaceAll("[^a-zA-Z0-9]", "-").toLowerCase());
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN));
    }
}
