package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;

import java.util.List;

public interface CertificationsStatisticsExportService {

    /**
     * Exports certifications statistics with optional filtering.
     * that means if years is null or empty, all available years are included
     * if office is null, all offices are included
     * if federalState is null, all federal states are included
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office (issuer) to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A ExportContent object containing the exported data.
     */
    ExportContent exportCertificationsStatistics(
            List<Integer> years,
            String office,
            String federalState
    );
}
