package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.export.ExportContent;

import java.util.List;

public interface LicensesStatisticsExportService {

    /**
     * Exports licenses statistics for a specific license type with optional filtering.
     * that means if years is null or empty, all available years are included
     * if office is null, all offices are included
     * if federalState is null, all federal states are included
     *
     * @param licenseType  The type of license (e.g., REGULAR, LIMITED, VACATION).
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A ExportContent object containing the exported data.
     */
    ExportContent exportLicensesStatistics(
            LicenseType licenseType,
            List<Integer> years,
            String office,
            String federalState
    );
}
