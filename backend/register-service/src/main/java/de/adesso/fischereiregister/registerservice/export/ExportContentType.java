package de.adesso.fischereiregister.registerservice.export;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.MediaType;

import java.nio.charset.StandardCharsets;

@AllArgsConstructor
@Getter
public enum ExportContentType {
    PDF("pdf", MediaType.APPLICATION_PDF),
    CSV("csv", new MediaType("text", "csv", StandardCharsets.UTF_8)); // RFC 4180 states that a csv file must be of type text/csv

    private final String extension;
    private final MediaType mediaType;

}
