package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatisticsExportService;
import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatisticsExportService;
import de.adesso.fischereiregister.registerservice.statistics.errors_statistics.ErrorsStatisticsExportService;
import de.adesso.fischereiregister.registerservice.statistics.inspections_statistics.InspectionsStatisticsExportService;
import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatisticsExportService;
import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatisticsExportService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsExportController implements api.StatisticsExportApi {

    private final BansStatisticsExportService bansStatisticsExportService;
    private final CertificationsStatisticsExportService certificationsStatisticsExportService;
    private final ErrorsStatisticsExportService errorsStatisticsExportService;
    private final InspectionsStatisticsExportService inspectionsStatisticsExportService;
    private final LicensesStatisticsExportService licensesStatisticsExportService;
    private final TaxesStatisticsExportService taxesStatisticsExportService;

    @Override
    public ResponseEntity<?> statisticsExportControllerExportBansStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export bans statistics for years: {}, federalState: {}", year, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = bansStatisticsExportService.exportBansStatistics(federalStateValue, year);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting bans statistics for years {}, federalState {}: {}", year, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportCertificationsStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export certifications statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = certificationsStatisticsExportService.exportCertificationsStatistics(year, office, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting certifications statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportErrorsStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export errors statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = errorsStatisticsExportService.exportErrorsStatistics(federalStateValue, office, year);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting errors statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportInspectionsStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export inspections statistics for years: {}, federalState: {}", year, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = inspectionsStatisticsExportService.exportInspectionsStatistics(year, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting inspections statistics for years {}, federalState {}: {}", year, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportLimitedLicensesStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export limited licenses statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = licensesStatisticsExportService.exportLicensesStatistics(LicenseType.LIMITED, year, office, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting limited licenses statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportRegularLicensesStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export regular licenses statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = licensesStatisticsExportService.exportLicensesStatistics(LicenseType.REGULAR, year, office, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting regular licenses statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportTaxesStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export taxes statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = taxesStatisticsExportService.exportTaxesStatistics(year, office, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting taxes statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public ResponseEntity<?> statisticsExportControllerExportVacationLicensesStatistics(@Valid List<Integer> year, @Valid String office, @Valid FederalStateAbbreviation federalState) {
        try {
            log.info("Received request to export vacation licenses statistics for years: {}, office: {}, federalState: {}", year, office, federalState);

            final String federalStateValue = federalState != null ? federalState.getValue() : null;
            final ExportContent exportContent = licensesStatisticsExportService.exportLicensesStatistics(LicenseType.VACATION, year, office, federalStateValue);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + exportContent.getFullFilename() + "\"")
                    .contentType(exportContent.type().getMediaType())
                    .body(new ByteArrayResource(exportContent.content()));

        } catch (Exception e) {
            log.error("Error exporting vacation licenses statistics for years {}, office {}, federalState {}: {}", year, office, federalState, e.getMessage(), e);
            throw e;
        }
    }
}
