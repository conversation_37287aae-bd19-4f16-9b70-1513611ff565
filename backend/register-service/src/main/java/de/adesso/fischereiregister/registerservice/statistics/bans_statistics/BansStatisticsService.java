package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import java.util.List;

public interface BansStatisticsService {

    /**
     * gets the number of active bans
     *
     * @return The number of active bans.
     */
    Integer getActiveBansAmount();

    /**
     * gets the number of active bans for a given federal state
     *
     * @param federalState The federal state for which the active bans are counted.
     * @return The number of active bans in the specified federal state.
     */
    Integer getActiveBansAmountByFederalState(String federalState);

    /**
     * Retrieves bans statistics with optional filtering.
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed bans statistics domain objects.
     */
    List<BansStatistics> getBansStatistics(
            List<Integer> years,
            String federalState
    );

    /**
     * gets the available years for bans statistics
     *
     * @return A list of years for which bans statistics are available.
     */
    List<Integer> getAvailableYears();

}
