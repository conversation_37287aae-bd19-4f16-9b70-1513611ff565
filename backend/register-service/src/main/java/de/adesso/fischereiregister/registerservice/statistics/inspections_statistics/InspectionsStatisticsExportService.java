package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;

import java.util.List;

public interface InspectionsStatisticsExportService {

    /**
     * Exports inspections statistics with optional filtering.
     * that means if years is null or empty, all available years are included
     * if office is null, all offices are included
     * if federalState is null, all federal states are included
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param federalState The federal state to filter by (optional).
     * @return A ExportContent object containing the exported data.
     */
    ExportContent exportInspectionsStatistics(
            List<Integer> years,
            String federalState
    );
}
