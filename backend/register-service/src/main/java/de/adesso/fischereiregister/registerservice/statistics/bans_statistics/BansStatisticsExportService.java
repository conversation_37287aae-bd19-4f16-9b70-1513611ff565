package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;

import java.util.List;

public interface BansStatisticsExportService {
    /**
     * Exports bans statistics with optional filtering.
     * that means if years is null or empty, all available years are included
     * if office is null, all offices are included
     * if federalState is null, all federal states are included
     *
     * @param federalState The federal state to filter by (optional). If null, all federal states are included.
     * @param years        The list of years for which the statistics are requested.
     * @return A ExportContent object containing the exported data.
     */
    ExportContent exportBansStatistics(String federalState, List<Integer> years);
}
