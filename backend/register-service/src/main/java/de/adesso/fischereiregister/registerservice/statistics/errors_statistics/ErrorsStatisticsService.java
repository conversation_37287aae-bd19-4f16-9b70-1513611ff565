package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import java.util.List;

public interface ErrorsStatisticsService {

    /**
     * Retrieves errors statistics with optional filtering.
     * if years is null or empty, all available years are included
     * if federalState is null, all federal states are included
     * if office is null, all offices are included
     *
     * @param years        The list of years to include. If null or empty, all available years are included.
     * @param office       The office to filter by (optional).
     * @param federalState The federal state to filter by (optional).
     * @return A list of transformed errors statistics domain objects.
     */
    List<ErrorsStatistics> getErrorsStatistics(
            List<Integer> years,
            String office,
            String federalState
    );

    /**
     * gets all available years for which error statistics exist
     *
     * @return A list of available years.
     */
    List<Integer> getAvailableYears();
}
