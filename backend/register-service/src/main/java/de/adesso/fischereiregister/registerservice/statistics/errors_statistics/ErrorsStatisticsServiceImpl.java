package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
public class ErrorsStatisticsServiceImpl implements ErrorsStatisticsService {

    private final ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    @Override
    public List<ErrorsStatistics> getErrorsStatistics(List<Integer> years, String federalState, String office) {
        try {
            log.debug("Fetching error statistics for federal state: {}, office: {} and years: {}", federalState, office, years);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : getAvailableYears();

            List<ErrorsStatistics> result = new ArrayList<>();

            // For each requested year, get counts for each error type
            for (Integer year : yearsToQuery) {
               if (federalState != null && !federalState.isEmpty() && office != null && !office.isEmpty()) {
                    int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                    int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);
                    int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(year, federalState, office);

                    result.add(new ErrorsStatistics(year, new ErrorsStatisticsData(onlineServiceErrors, cardErrors, systemErrors)));
                } else {
                    int onlineServiceErrors = errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(year);
                    int cardErrors = errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(year);
                    int systemErrors = errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(year);

                    result.add(new ErrorsStatistics(year, new ErrorsStatisticsData(onlineServiceErrors, cardErrors, systemErrors)));
                }
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(ErrorsStatistics::year).reversed());

            log.debug("Successfully fetched error statistics for federal state: {}, office: {} and years: {}", federalState, office, years);

            return result;

        } catch (Exception e) {
            log.error("Error fetching error statistics for federal state: {}, office: {} and years: {}: {}", federalState, office, years, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch error statistics", e);
        }
    }

    @Override
    public List<Integer> getAvailableYears() {
        try {
            log.debug("Fetching available years for error statistics");

            List<Integer> availableYears = errorsProtocolStatisticsPort.getAvailableYears();

            log.debug("Successfully fetched available years for error statistics: {}", availableYears);

            return availableYears;
        } catch (Exception e) {
            log.error("Error fetching available years for error statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch available years", e);
        }
    }
}
