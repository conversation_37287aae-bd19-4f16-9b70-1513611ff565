package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class InspectionsStatisticsExportServiceImpl implements InspectionsStatisticsExportService {

    private static final String FILENAME_PREFIX = "inspections-statistics";
    private static final String DATETIME_PATTERN = "yyyyMMdd-HHmmss";

    private final InspectorProtocolService inspectorProtocolService;

    @Override
    public ExportContent exportInspectionsStatistics(
            List<Integer> years,
            String federalState) {
        try {
            log.debug("Exporting inspections statistics for years: {}, federalState: {}",
                    years, federalState);

            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : inspectorProtocolService.getAvailableYears();

            final ExportContent exportContent = generateInspectionsStatisticsCsv(federalState, yearsToQuery);

            log.info("Inspections statistics exported: {}", exportContent.getFullFilename());

            return exportContent;

        } catch (Exception e) {
            log.error("Error exporting inspections statistics for years {}, federalState {}: {}",
                    years, federalState, e.getMessage(), e);
            throw new RuntimeException("Failed to export inspections statistics", e);
        }
    }

    private ExportContent generateInspectionsStatisticsCsv(String federalState, List<Integer> years) throws IOException {
        StringWriter stringWriter = new StringWriter();
        CSVFormat csvFormat = CSVFormat.DEFAULT.builder()
                .setHeader("Jahr", "Bundesland", "Anzahl der Kontrollen", "Anzahl der Kontrolleure im Zeitraum")
                .build();

        try (CSVPrinter csvPrinter = new CSVPrinter(stringWriter, csvFormat)) {
            if (federalState != null && !federalState.isEmpty()) {
                // Export for specific federal state
                List<InspectionsStatisticsResult> results = inspectorProtocolService.getInspectionsStatistics(years, federalState);
                for (InspectionsStatisticsResult result : results) {
                    csvPrinter.printRecord(
                            result.getYear(),
                            federalState,
                            result.getData().getNumberOfInspections(),
                            result.getData().getActiveInspectors()
                    );
                }
            } else {
                // Export for all federal states
                for (FederalState fs : FederalState.values()) {
                    List<InspectionsStatisticsResult> results = inspectorProtocolService.getInspectionsStatistics(years, fs.name());
                    for (InspectionsStatisticsResult result : results) {
                        csvPrinter.printRecord(
                                result.getYear(),
                                fs.name(),
                                result.getData().getNumberOfInspections(),
                                result.getData().getActiveInspectors()
                        );
                    }
                }
            }
        }

        String filename = buildFilename(federalState);
        byte[] content = stringWriter.toString().getBytes(StandardCharsets.UTF_8);

        return new ExportContent(filename, ExportContentType.CSV, content);
    }

    private String buildFilename(String federalState) {
        StringBuilder filename = new StringBuilder(FILENAME_PREFIX);

        if (federalState != null && !federalState.isEmpty()) {
            filename.append("-").append(federalState.toLowerCase());
        }

        filename.append("-").append(getCurrentTimestamp());

        return filename.toString();
    }

    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATETIME_PATTERN));
    }
}
