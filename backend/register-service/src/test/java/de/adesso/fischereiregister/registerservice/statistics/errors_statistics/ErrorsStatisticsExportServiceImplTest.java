package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ErrorsStatisticsExportServiceImplTest {

    @Mock
    private ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    private ErrorsStatisticsExportServiceImpl exportService;

    @BeforeEach
    void setUp() {
        exportService = new ErrorsStatisticsExportServiceImpl(errorsProtocolStatisticsPort);
    }

    @Test
    @DisplayName("exportErrorsStatistics should export CSV with federal state filter")
    void exportErrorsStatistics_WithFederalState_ShouldExportCsv() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2023, 2024);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(5);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(8);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(6);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(4);

        // when
        ExportContent result = exportService.exportErrorsStatistics(federalState, null, years);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("errors-statistics-sh-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Online Service Fehler,Karten Fehler,System Fehler");
        assertThat(csvContent).contains("2023,SH,5,3,2");
        assertThat(csvContent).contains("2024,SH,8,6,4");

        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null);
        verify(errorsProtocolStatisticsPort).getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null);
        verify(errorsProtocolStatisticsPort).getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null);
        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null);
        verify(errorsProtocolStatisticsPort).getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null);
        verify(errorsProtocolStatisticsPort).getSystemErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null);
        verifyNoMoreInteractions(errorsProtocolStatisticsPort);
    }

    @Test
    @DisplayName("exportErrorsStatistics should export CSV for all federal states when no filter")
    void exportErrorsStatistics_WithoutFederalState_ShouldExportAllStates() {
        // given
        List<Integer> years = List.of(2023);

        // Mock responses for all federal states (just a few for testing)
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);

        // when
        ExportContent result = exportService.exportErrorsStatistics(null, null, years);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("errors-statistics-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Online Service Fehler,Karten Fehler,System Fehler");
        assertThat(csvContent).contains("2023,BW,1,1,1");
        assertThat(csvContent).contains("2023,BY,1,1,1");
        assertThat(csvContent).contains("2023,SH,1,1,1");

        // Verify that all federal states were queried (16 federal states * 3 error types)
        verify(errorsProtocolStatisticsPort, times(16)).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null));
        verify(errorsProtocolStatisticsPort, times(16)).getCardOrderErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null));
        verify(errorsProtocolStatisticsPort, times(16)).getSystemErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null));
    }

    @Test
    @DisplayName("exportErrorsStatistics should use available years when years list is empty")
    void exportErrorsStatistics_WithEmptyYears_ShouldUseAvailableYears() {
        // given
        String federalState = "SH";
        String office = null;
        List<Integer> years = List.of();
        List<Integer> availableYears = List.of(2022, 2023);

        when(errorsProtocolStatisticsPort.getAvailableYears()).thenReturn(availableYears);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);

        // when
        ExportContent result = exportService.exportErrorsStatistics(federalState, office, years);

        // then
        assertThat(result).isNotNull();
        verify(errorsProtocolStatisticsPort).getAvailableYears();
        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null);
        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null);
    }

    @Test
    @DisplayName("exportErrorsStatistics should handle multiple years")
    void exportErrorsStatistics_WithMultipleYears_ShouldExportAllYears() {
        // given
        String federalState = "SH";
        String office = null;
        List<Integer> years = List.of(2022, 2023, 2024);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null)).thenReturn(3);

        // when
        ExportContent result = exportService.exportErrorsStatistics(federalState, office, years);

        // then
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("2022,SH,1,1,1");
        assertThat(csvContent).contains("2023,SH,2,2,2");
        assertThat(csvContent).contains("2024,SH,3,3,3");

        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2022, federalState, null);
        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null);
        verify(errorsProtocolStatisticsPort).getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2024, federalState, null);
    }

    @Test
    @DisplayName("exportErrorsStatistics should handle exceptions")
    void exportErrorsStatistics_ShouldHandleExceptions() {
        // given
        String federalState = "SH";
        String office = null;
        List<Integer> years = List.of(2023);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null))
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> exportService.exportErrorsStatistics(federalState, office, years))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to export errors statistics");
    }

    @Test
    @DisplayName("exportErrorsStatistics should generate correct filename with federal state")
    void exportErrorsStatistics_ShouldGenerateCorrectFilenameWithFederalState() {
        // given
        String federalState = "SH";
        String office = null;
        List<Integer> years = List.of(2023);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, null)).thenReturn(1);

        // when
        ExportContent result = exportService.exportErrorsStatistics(federalState, office, years);

        // then
        assertThat(result.getFullFilename()).matches("errors-statistics-sh-\\d{8}-\\d{6}\\.csv");
    }

    @Test
    @DisplayName("exportErrorsStatistics should generate correct filename without federal state")
    void exportErrorsStatistics_ShouldGenerateCorrectFilenameWithoutFederalState() {
        // given
        String federalState = null;
        String office = null;
        List<Integer> years = List.of(2023);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(eq(2023), anyString(), eq(null))).thenReturn(1);

        // when
        ExportContent result = exportService.exportErrorsStatistics(federalState, office, years);

        // then
        assertThat(result.getFullFilename()).matches("errors-statistics-\\d{8}-\\d{6}\\.csv");
    }
}