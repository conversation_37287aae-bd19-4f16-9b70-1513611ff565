package de.adesso.fischereiregister.registerservice.statistics.inspections_statistics;

import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResultData;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InspectionsStatisticsExportServiceImplTest {

    @Mock
    private InspectorProtocolService inspectorProtocolService;

    @InjectMocks
    private InspectionsStatisticsExportServiceImpl exportService;

    @Test
    @DisplayName("InspectionsStatisticsExportServiceImpl.exportInspectionsStatistics should return CSV content")
    void exportInspectionsStatistics_shouldReturnCsvContent() {
        // Given
        List<Integer> years = List.of(2023);
        String federalState = "SH";
        List<InspectionsStatisticsResult> mockResults = List.of(
                createResult(2023, 10, 5)
        );

        when(inspectorProtocolService.getInspectionsStatistics(years, federalState))
                .thenReturn(mockResults);

        // When
        ExportContent result = exportService.exportInspectionsStatistics(years, federalState);

        // Then
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(new String(result.content(), StandardCharsets.UTF_8))
                .contains("2023,SH,10,5");
    }

    @Test
    @DisplayName("exportInspectionsStatistics should use available years when years list is empty")
    void exportInspectionsStatistics_withEmptyYears_shouldUseAllAvailableYears() {
        // Given
        List<Integer> emptyYears = List.of();
        List<Integer> availableYears = List.of(2022, 2023);
        String federalState = "SH";

        when(inspectorProtocolService.getAvailableYears()).thenReturn(availableYears);
        when(inspectorProtocolService.getInspectionsStatistics(availableYears, federalState))
                .thenReturn(List.of(createResult(2022, 5, 2)));

        // When
        exportService.exportInspectionsStatistics(emptyYears, federalState);

        // Then
        verify(inspectorProtocolService).getAvailableYears();
        verify(inspectorProtocolService).getInspectionsStatistics(availableYears, federalState);
    }

    @Test
    @DisplayName("exportInspectionsStatistics should query all federal states when federalState is null")
    void exportInspectionsStatistics_withNullFederalState_shouldQueryAllStates() {
        // Given
        List<Integer> years = List.of(2023);

        // When
        exportService.exportInspectionsStatistics(years, null);

        // Then
        verify(inspectorProtocolService, atLeastOnce()).getInspectionsStatistics(any(), any());
    }

    @Test
    @DisplayName("exportInspectionsStatistics should query all federal states when federalState is empty")
    void exportInspectionsStatistics_withEmptyFederalState_shouldQueryAllStates() {
        // Given
        List<Integer> years = List.of(2023);
        String federalState = "";

        // When
        exportService.exportInspectionsStatistics(years, federalState);

        // Then
        verify(inspectorProtocolService, atLeastOnce()).getInspectionsStatistics(any(), any());
    }

    private InspectionsStatisticsResult createResult(int year,
                                                     int inspections, int inspectors) {
        InspectionsStatisticsResultData data = new InspectionsStatisticsResultData();
        data.setNumberOfInspections(inspections);
        data.setActiveInspectors(inspectors);

        InspectionsStatisticsResult result = new InspectionsStatisticsResult();
        result.setYear(year);
        result.setData(data);
        return result;
    }
}