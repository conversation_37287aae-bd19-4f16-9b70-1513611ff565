package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.consent.TaxConsentInfo;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.IdentificationDocumentType;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.ban.eventhandling.BanViewEventHandler;
import de.adesso.fischereiregister.view.certifications_statistics.eventhandling.CertificationsStatisticsViewEventHandler;
import de.adesso.fischereiregister.view.licenses_statistics.eventhandling.LicensesStatisticsViewEventHandler;
import de.adesso.fischereiregister.view.taxes_statistics.eventhandling.TaxesStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.matchesPattern;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class StatisticsExportReadIntegrationTest {

    private static final FederalState TEST_FEDERAL_STATE_SH = FederalState.SH;
    private static final String TEST_OFFICE_1 = "TestOffice1";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final Instant CURRENT_YEAR_INSTANT = LocalDate.of(CURRENT_YEAR, 1, 1).atStartOfDay().toInstant(ZoneOffset.UTC);

    @Autowired
    private MockMvc mvc;

    @Autowired
    private BanViewEventHandler banViewEventHandler;

    @Autowired
    private CertificationsStatisticsViewEventHandler certificationsStatisticsViewEventHandler;

    @Autowired
    private LicensesStatisticsViewEventHandler licensesStatisticsViewEventHandler;

    @Autowired
    private TaxesStatisticsViewEventHandler taxesStatisticsViewEventHandler;

    @BeforeAll
    void setUp() {
        // Create test data for bans statistics
        BannedEvent banEventSH1 = createBanEvent(CURRENT_YEAR);
        banViewEventHandler.on(banEventSH1);

        // Create test data for certifications statistics
        QualificationsProofCreatedEvent certificationEventSH1 = createCertificationEvent(CURRENT_YEAR);
        certificationsStatisticsViewEventHandler.on(certificationEventSH1);

        // Create test data for licenses statistics
        RegularLicenseDigitizedEvent digitizedEvent = createDigitizedLicenseEvent(CURRENT_YEAR);
        licensesStatisticsViewEventHandler.on(digitizedEvent, CURRENT_YEAR_INSTANT);

        // Create test data for taxes statistics
        FishingTaxPayedEvent taxEvent = createTaxEvent();
        taxesStatisticsViewEventHandler.on(taxEvent, CURRENT_YEAR_INSTANT);
    }

    private BannedEvent createBanEvent(int year) {
        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(TEST_FEDERAL_STATE_SH.toString());

        // Create a ban with specific year for at and from dates
        Ban ban = new Ban();
        ban.setBanId(UUID.randomUUID());
        ban.setFileNumber("TEST-BAN-" + System.currentTimeMillis());
        ban.setReportedBy("Test Office");
        ban.setAt(LocalDate.of(year, 1, 1)); // Set the issue date in the specified year
        ban.setFrom(LocalDate.of(year, 12, 31)); // Set from date in the specified year
        ban.setTo(null); // Permanent ban

        // Create BannedEvent with the created ban
        return new BannedEvent(UUID.randomUUID(), ban, jurisdiction, "Test Office");
    }

    private QualificationsProofCreatedEvent createCertificationEvent(int year) {
        // Create a qualification proof with specific year
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFederalState(TEST_FEDERAL_STATE_SH.toString());
        qualificationsProof.setIssuedBy(TEST_OFFICE_1);
        qualificationsProof.setPassedOn(LocalDate.of(year, 1, 15)); // Set the passed date in the specified year
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId("CERT-" + UUID.randomUUID());

        // Create QualificationsProofCreatedEvent with the created proof
        return new QualificationsProofCreatedEvent(
                UUID.randomUUID(), // registerEntryId
                qualificationsProof,
                new Person() // person
        );
    }

    private RegularLicenseDigitizedEvent createDigitizedLicenseEvent(int year) {
        // Create two qualification proofs for the digitized license
        QualificationsProof proof1 = new QualificationsProof();
        proof1.setFederalState(TEST_FEDERAL_STATE_SH.toString());
        proof1.setIssuedBy(TEST_OFFICE_1);
        proof1.setPassedOn(LocalDate.of(year, 2, 20));
        proof1.setType(QualificationsProofType.CERTIFICATE);
        proof1.setFishingCertificateId("CERT-DIG-1-" + UUID.randomUUID());

        FishingLicense fishingLicense =  new FishingLicense();
        fishingLicense.setType(LicenseType.REGULAR);

        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(TEST_FEDERAL_STATE_SH.toString());

        // Create RegularLicenseDigitizedEvent with the proofs
        return new RegularLicenseDigitizedEvent(
                UUID.randomUUID(), // registerId
                "salt",
                new Person(), // person
                jurisdiction, // jurisdiction
                fishingLicense, // fishingLicense
                new ArrayList<>(), // fees
                new ArrayList<>(), // taxes
                List.of(proof1), // qualificationsProofs
                new ArrayList<>(), // identificationDocuments
                mock(JurisdictionConsentInfo.class), // consentInfo
                "Test Office", // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    private FishingTaxPayedEvent createTaxEvent() {
        // Create person
        Person person = DomainTestData.createPerson();

        // Create tax
        Tax tax = DomainTestData.createAnalogTax();

        // Create identification document with tax
        IdentificationDocument identificationDocument = new IdentificationDocument();
        identificationDocument.setDocumentId(UUID.randomUUID().toString());
        identificationDocument.setType(IdentificationDocumentType.PDF);
        identificationDocument.setIssuedDate(LocalDate.now());
        identificationDocument.setTax(tax);
        identificationDocument.setValidFrom(LocalDate.now());
        identificationDocument.setValidTo(LocalDate.now().plusYears(1));

        List<IdentificationDocument> identificationDocuments = new ArrayList<>();
        identificationDocuments.add(identificationDocument);

        // Create tax consent info
        TaxConsentInfo consentInfo = DomainTestData.createTaxConsentInfo();

        return new FishingTaxPayedEvent(
                UUID.randomUUID(),
                consentInfo,
                person,
                DomainTestData.createAnalogTaxesWithOneTax(),
                "anySalt",
                identificationDocuments,
                TEST_OFFICE_1,
                null, // inboxReference
                null, // serviceAccountId
                null, // transactionId
                SubmissionType.ANALOG
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans/export
            Verify that the bans statistics export endpoint returns CSV with correct filename.
            """)
    void callExportBansStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"bans-statistics-sh-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Jahr,Bundesland,Neu verhängte Sperren,Gestartete Sperren")));
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications/export
            Verify that the certifications statistics export endpoint returns CSV with correct filename.
            """)
    void callExportCertificationsStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"certifications-statistics-sh-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Jahr,Bundesland,Prüfungsinstitution,Anzahl ausgestellter Prüfungen")));
    }

    @Test
    @DisplayName("""
            GET /api/statistics/licenses/regular/export
            Verify that the regular licenses statistics export endpoint returns CSV with correct filename.
            """)
    void callExportLicensesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/licenses/regular/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .param("licenseType", "REGULAR")
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"licenses-statistics-sh-regular-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Jahr,Bundesland,Behörde,Scheintyp,Antragsweg,Anzahl")));
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes/export
            Verify that the taxes statistics export endpoint returns CSV with correct filename.
            """)
    void callExportTaxesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"taxes-statistics-sh-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Bundesland,Jahr,Behörde,Antragsweg,Gültigkeitszeitraum,Anzahl,Betrag")));
    }

    @Test
    @DisplayName("""
            GET /api/statistics/errors/export
            Verify that the errors statistics export endpoint returns CSV with correct filename.
            """)
    void callExportErrorsStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/errors/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"errors-statistics-sh-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Jahr,Bundesland,Online Service Fehler,Karten Fehler,System Fehler")));
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections/export
            Verify that the inspections statistics export endpoint returns CSV with correct filename.
            """)
    void callExportInspectionsStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections/export")
                        .param("year", String.valueOf(CURRENT_YEAR))
                        .param("federalState", TEST_FEDERAL_STATE_SH.toString())
                        .accept("text/csv"))
                .andExpect(status().isOk())
                .andExpect(content().contentType("text/csv;charset=UTF-8"))
                .andExpect(header().string("Content-Disposition",
                    matchesPattern("attachment; filename=\"inspections-statistics-sh-\\d{8}-\\d{6}\\.csv\"")))
                .andExpect(content().string(containsString("Jahr,Bundesland,Anzahl der Kontrollen,Anzahl der Kontrolleure im Zeitraum")));
    }

}