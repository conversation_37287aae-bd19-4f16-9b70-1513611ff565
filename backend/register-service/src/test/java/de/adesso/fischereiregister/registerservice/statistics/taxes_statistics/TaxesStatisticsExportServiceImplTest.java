package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaxesStatisticsExportServiceImplTest {

    @Mock
    private TaxesStatisticsViewService taxesStatisticsViewService;

    @InjectMocks
    private TaxesStatisticsExportServiceImpl exportService;

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics should return CSV with correct structure")
    void exportTaxesStatistics_shouldReturnCsvWithCorrectStructure() {
        // Given
        List<Integer> years = List.of(2023);
        TaxesStatisticsView view = createView("SH", "Office1", 2023, SubmissionType.ONLINE, 1, 5, 100.50);

        when(taxesStatisticsViewService.getStatisticsByYears(years)).thenReturn(List.of(view));

        // When
        ExportContent result = exportService.exportTaxesStatistics(years, null, null);

        // Then
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Bundesland,Jahr,Behörde,Antragsweg,Gültigkeitszeitraum,Anzahl,Betrag");
        assertThat(csvContent).contains("SH,2023,Office1,ONLINE,1,5,100.5");
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics with empty years should use available years")
    void exportTaxesStatistics_withEmptyYears_shouldUseAvailableYears() {
        // Given
        List<Integer> emptyYears = List.of();
        List<Integer> availableYears = List.of(2022, 2023);

        when(taxesStatisticsViewService.getAvailableYears()).thenReturn(availableYears);
        when(taxesStatisticsViewService.getStatisticsByYears(availableYears)).thenReturn(List.of());

        // When
        exportService.exportTaxesStatistics(emptyYears, null, null);

        // Then
        verify(taxesStatisticsViewService).getAvailableYears();
        verify(taxesStatisticsViewService).getStatisticsByYears(availableYears);
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics with office should prioritize federal state over office")
    void exportTaxesStatistics_withOffice_shouldPrioritizeOffice() {
        // Given
        List<Integer> years = List.of(2023);
        String office = "Office1";
        String federalState = "SH";

        when(taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years)).thenReturn(List.of());

        // When
        exportService.exportTaxesStatistics(years, office, federalState);

        // Then
        verify(taxesStatisticsViewService).getStatisticsByFederalStateAndYears(federalState, years);
        verify(taxesStatisticsViewService, never()).getStatisticsByOfficeAndYears(anyString(), anyList());
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics with federal state should query by federal state")
    void exportTaxesStatistics_withFederalState_shouldQueryByFederalState() {
        // Given
        List<Integer> years = List.of(2023);
        String federalState = "SH";

        when(taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years)).thenReturn(List.of());

        // When
        exportService.exportTaxesStatistics(years, null, federalState);

        // Then
        verify(taxesStatisticsViewService).getStatisticsByFederalStateAndYears(federalState, years);
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics should sort results by year descending")
    void exportTaxesStatistics_shouldSortByYearDescending() {
        // Given
        List<Integer> years = List.of(2022, 2023);
        TaxesStatisticsView view1 = createView("SH", "Office1", 2022, SubmissionType.ONLINE, 1, 1, 10.00);
        TaxesStatisticsView view2 = createView("SH", "Office1", 2023, SubmissionType.ONLINE, 1, 2, 20.00);

        when(taxesStatisticsViewService.getStatisticsByYears(years)).thenReturn(List.of(view1, view2));

        // When
        ExportContent result = exportService.exportTaxesStatistics(years, null, null);

        // Then
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent.indexOf("2023")).isLessThan(csvContent.indexOf("2022"));
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics should generate correct filename with office")
    void exportTaxesStatistics_shouldGenerateCorrectFilenameWithOffice() {
        // Given
        List<Integer> years = List.of(2023);
        String office = "Test Office";

        when(taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, years)).thenReturn(List.of());

        // When
        ExportContent result = exportService.exportTaxesStatistics(years, office, null);

        // Then
        assertThat(result.getFullFilename()).matches("taxes-statistics-test-office-\\d{8}-\\d{6}\\.csv");
    }

    @Test
    @DisplayName("TaxesStatisticsExportServiceImpl.exportTaxesStatistics should generate correct filename with federal state")
    void exportTaxesStatistics_shouldGenerateCorrectFilenameWithFederalState() {
        // Given
        List<Integer> years = List.of(2023);
        String federalState = "SH";

        when(taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years)).thenReturn(List.of());

        // When
        ExportContent result = exportService.exportTaxesStatistics(years, null, federalState);

        // Then
        assertThat(result.getFullFilename()).matches("taxes-statistics-sh-\\d{8}-\\d{6}\\.csv");
    }

    private TaxesStatisticsView createView(String federalState, String office, int year,
                                           SubmissionType submissionType, int duration, int count, Double revenue) {
        TaxesStatisticsView view = new TaxesStatisticsView();
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setYear(year);
        view.setSource(submissionType);
        view.setDuration(duration);
        view.setCount(count);
        view.setRevenue(revenue);
        return view;
    }
}