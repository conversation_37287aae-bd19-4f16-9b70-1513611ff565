package de.adesso.fischereiregister.registerservice.statistics.licenses_statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LicensesStatisticsExportServiceImplTest {

    @Mock
    private LicensesStatisticsViewService licensesStatisticsViewService;

    @InjectMocks
    private LicensesStatisticsExportServiceImpl exportService;

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics should return CSV content with correct structure")
    void exportLicensesStatistics_shouldReturnCsvContent() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2023);
        String office = "TestOffice";

        LicensesStatisticsView view = createView("SH", office, 2023, licenseType, SubmissionType.ONLINE, 5);
        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, years))
                .thenReturn(List.of(view));

        // When
        ExportContent result = exportService.exportLicensesStatistics(licenseType, years, office, null);

        // Then
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Behörde,Scheintyp,Antragsweg,Anzahl");
        assertThat(csvContent).contains("2023,SH,TestOffice,REGULAR,ONLINE,5");
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics with empty years should use available years")
    void exportLicensesStatistics_withEmptyYears_shouldUseAvailableYears() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> emptyYears = List.of();
        List<Integer> availableYears = List.of(2022, 2023);

        when(licensesStatisticsViewService.getAvailableYears()).thenReturn(availableYears);
        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, availableYears))
                .thenReturn(List.of());

        // When
        exportService.exportLicensesStatistics(licenseType, emptyYears, null, null);

        // Then
        verify(licensesStatisticsViewService).getAvailableYears();
        verify(licensesStatisticsViewService).getStatisticsByLicenseTypeAndYears(licenseType, availableYears);
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics with office should prioritize federal state over office")
    void exportLicensesStatistics_withOffice_shouldPrioritizeOffice() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2023);
        String office = "TestOffice";
        String federalState = "SH";

        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, years))
                .thenReturn(List.of());

        // When
        exportService.exportLicensesStatistics(licenseType, years, office, federalState);

        // Then
        verify(licensesStatisticsViewService).getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, years);
        verify(licensesStatisticsViewService, never()).getStatisticsByLicenseTypeAndOfficeAndYears(any(), any(), any());
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics with federal state should query by federal state")
    void exportLicensesStatistics_withFederalState_shouldQueryByFederalState() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2023);
        String federalState = "SH";

        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, years))
                .thenReturn(List.of());

        // When
        exportService.exportLicensesStatistics(licenseType, years, null, federalState);

        // Then
        verify(licensesStatisticsViewService).getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalState, years);
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics without filters should query all data")
    void exportLicensesStatistics_withoutFilters_shouldQueryAllData() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2023);

        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, years))
                .thenReturn(List.of());

        // When
        exportService.exportLicensesStatistics(licenseType, years, null, null);

        // Then
        verify(licensesStatisticsViewService).getStatisticsByLicenseTypeAndYears(licenseType, years);
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics should sort results by year descending")
    void exportLicensesStatistics_shouldSortByYearDescending() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2022, 2023);

        LicensesStatisticsView view1 = createView("SH", "Office1", 2022, licenseType, SubmissionType.ONLINE, 1);
        LicensesStatisticsView view2 = createView("SH", "Office1", 2023, licenseType, SubmissionType.ONLINE, 2);

        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, years))
                .thenReturn(List.of(view1, view2));

        // When
        ExportContent result = exportService.exportLicensesStatistics(licenseType, years, null, null);

        // Then
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent.indexOf("2023")).isLessThan(csvContent.indexOf("2022"));
    }

    @Test
    @DisplayName("LicensesStatisticsExportServiceImpl.exportLicensesStatistics should generate correct filename with office")
    void exportLicensesStatistics_shouldGenerateCorrectFilenameWithOffice() {
        // Given
        LicenseType licenseType = LicenseType.REGULAR;
        List<Integer> years = List.of(2023);
        String office = "Test Office";

        when(licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, years))
                .thenReturn(List.of());

        // When
        ExportContent result = exportService.exportLicensesStatistics(licenseType, years, office, null);

        // Then
        assertThat(result.getFullFilename()).matches("licenses-statistics-regular-test-office-\\d{8}-\\d{6}\\.csv");
    }

    private LicensesStatisticsView createView(String federalState, String office, int year,
                                              LicenseType licenseType, SubmissionType submissionType, int count) {
        LicensesStatisticsView view = new LicensesStatisticsView();
        view.setFederalState(federalState);
        view.setOffice(office);
        view.setYear(year);
        view.setLicenseType(licenseType);
        view.setSource(submissionType);
        view.setCount(count);
        return view;
    }
}