package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CertificationsStatisticsExportServiceImplTest {

    @Mock
    private CertificationsStatisticsViewService certificationsStatisticsViewService;

    private CertificationsStatisticsExportServiceImpl exportService;

    @BeforeEach
    void setUp() {
        exportService = new CertificationsStatisticsExportServiceImpl(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("exportCertificationsStatistics should export CSV with federal state filter")
    void exportCertificationsStatistics_WithFederalState_ShouldExportCsv() {
        // given
        List<Integer> years = List.of(2023, 2024);
        String federalState = "SH";
        String office = null;

        CertificationsStatisticsView view1 = createView("SH", "Office1", 2023, 5);
        CertificationsStatisticsView view2 = createView("SH", "Office2", 2024, 8);
        List<CertificationsStatisticsView> views = List.of(view1, view2);

        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years))
                .thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, office, federalState);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("certifications-statistics-sh-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Prüfungsinstitution,Anzahl ausgestellter Prüfungen");
        assertThat(csvContent).contains("2024,SH,Office2,8");
        assertThat(csvContent).contains("2023,SH,Office1,5");

        verify(certificationsStatisticsViewService).getStatisticsByFederalStateAndYears(federalState, years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("exportCertificationsStatistics should export CSV with office filter")
    void exportCertificationsStatistics_WithOffice_ShouldExportCsv() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = null;
        String office = "Test Office";

        CertificationsStatisticsView view = createView("SH", "TestOffice", 2023, 10);
        List<CertificationsStatisticsView> views = List.of(view);

        when(certificationsStatisticsViewService.getStatisticsByIssuerAndYears(office, years))
                .thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, office, federalState);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("certifications-statistics-test-office-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("2023,SH,TestOffice,10");

        verify(certificationsStatisticsViewService).getStatisticsByIssuerAndYears(office, years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("exportCertificationsStatistics should export CSV without filters")
    void exportCertificationsStatistics_WithoutFilters_ShouldExportCsv() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = null;
        String office = null;

        CertificationsStatisticsView view = createView("NW", "Office1", 2023, 3);
        List<CertificationsStatisticsView> views = List.of(view);

        when(certificationsStatisticsViewService.getStatisticsByYears(years))
                .thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, office, federalState);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("certifications-statistics-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("2023,NW,Office1,3");

        verify(certificationsStatisticsViewService).getStatisticsByYears(years);
        verifyNoMoreInteractions(certificationsStatisticsViewService);
    }

    @Test
    @DisplayName("exportCertificationsStatistics should use available years when years list is empty")
    void exportCertificationsStatistics_WithEmptyYears_ShouldUseAvailableYears() {
        // given
        List<Integer> years = List.of();
        List<Integer> availableYears = List.of(2022, 2023);
        String federalState = null;
        String office = null;

        CertificationsStatisticsView view = createView("SH", "Office1", 2023, 5);
        List<CertificationsStatisticsView> views = List.of(view);

        when(certificationsStatisticsViewService.getAvailableYears()).thenReturn(availableYears);
        when(certificationsStatisticsViewService.getStatisticsByYears(availableYears)).thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, office, federalState);

        // then
        assertThat(result).isNotNull();
        verify(certificationsStatisticsViewService).getAvailableYears();
        verify(certificationsStatisticsViewService).getStatisticsByYears(availableYears);
    }

    @Test
    @DisplayName("exportCertificationsStatistics should handle exceptions")
    void exportCertificationsStatistics_ShouldHandleExceptions() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = "SH";
        String office = null;

        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years))
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> exportService.exportCertificationsStatistics(years, office, federalState))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to export certifications statistics");
    }

    @Test
    @DisplayName("exportCertificationsStatistics should prioritize federal state over office")
    void exportCertificationsStatistics_WithBothFilters_ShouldPrioritizeFederalState() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = "SH";
        String office = "TestOffice";

        CertificationsStatisticsView view = createView("SH", "Office1", 2023, 5);
        List<CertificationsStatisticsView> views = List.of(view);

        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years))
                .thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, office, federalState);

        // then
        assertThat(result).isNotNull();
        assertThat(result.getFullFilename()).contains("sh");

        verify(certificationsStatisticsViewService).getStatisticsByFederalStateAndYears(federalState, years);
        verify(certificationsStatisticsViewService, never()).getStatisticsByIssuerAndYears(anyString(), anyList());
    }

    @Test
    @DisplayName("exportCertificationsStatistics should sort results by year descending")
    void exportCertificationsStatistics_ShouldSortByYearDescending() {
        // given
        List<Integer> years = List.of(2022, 2024, 2023);
        String federalState = "SH";

        CertificationsStatisticsView view1 = createView("SH", "Office1", 2022, 1);
        CertificationsStatisticsView view2 = createView("SH", "Office1", 2023, 2);
        CertificationsStatisticsView view3 = createView("SH", "Office1", 2024, 3);
        List<CertificationsStatisticsView> views = List.of(view1, view2, view3);

        when(certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalState, years))
                .thenReturn(views);

        // when
        ExportContent result = exportService.exportCertificationsStatistics(years, null, federalState);

        // then
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        String[] lines = csvContent.split("\n");
        
        // Check that 2024 comes before 2023, which comes before 2022
        assertThat(lines[1]).contains("2024"); // First data row
        assertThat(lines[2]).contains("2023"); // Second data row
        assertThat(lines[3]).contains("2022"); // Third data row
    }

    private CertificationsStatisticsView createView(String federalState, String issuer, int year, int count) {
        CertificationsStatisticsView view = new CertificationsStatisticsView();
        view.setFederalState(federalState);
        view.setIssuer(issuer);
        view.setYear(year);
        view.setCount(count);
        return view;
    }
}
