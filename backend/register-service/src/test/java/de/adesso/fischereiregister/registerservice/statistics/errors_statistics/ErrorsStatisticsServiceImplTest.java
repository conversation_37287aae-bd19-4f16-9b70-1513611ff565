package de.adesso.fischereiregister.registerservice.statistics.errors_statistics;

import de.adesso.fischereiregister.core.ports.ErrorsProtocolStatisticsPort;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ErrorsStatisticsServiceImplTest {

    @Mock
    private ErrorsProtocolStatisticsPort errorsProtocolStatisticsPort;

    @InjectMocks
    private ErrorsStatisticsServiceImpl service;

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getAvailableYears should return years from port")
    void getAvailableYears_ShouldReturnYearsFromPort() {
        // given
        List<Integer> expectedYears = List.of(2023, 2024, 2025);
        when(errorsProtocolStatisticsPort.getAvailableYears()).thenReturn(expectedYears);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertThat(result).isEqualTo(expectedYears);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getErrorsStatistics should return statistics for all years when no filters are provided")
    void getErrorsStatistics_ShouldReturnStatisticsForAllYearsWhenNoFilters() {
        // given
        List<Integer> years = List.of(2023, 2024);

        // Mock port responses for 2023
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2023)).thenReturn(5);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2023)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2023)).thenReturn(2);

        // Mock port responses for 2024 (no data)
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2024)).thenReturn(0);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2024)).thenReturn(0);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2024)).thenReturn(0);

        // when
        List<ErrorsStatistics> result = service.getErrorsStatistics(years, null, null);

        // then
        assertThat(result).hasSize(2);

        // Check 2023 data
        Optional<ErrorsStatistics> stats2023Optional = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst();
        assertThat(stats2023Optional).isPresent();
        ErrorsStatistics stats2023 = stats2023Optional.get();
        assertThat(stats2023.data().onlineService()).isEqualTo(5);
        assertThat(stats2023.data().cardPrinterService()).isEqualTo(3);
        assertThat(stats2023.data().system()).isEqualTo(2);

        // Check 2024 data (should be zero)
        Optional<ErrorsStatistics> stats2024Optional = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst();
        assertThat(stats2024Optional).isPresent();
        ErrorsStatistics stats2024 = stats2024Optional.get();
        assertThat(stats2024.data().onlineService()).isEqualTo(0);
        assertThat(stats2024.data().cardPrinterService()).isEqualTo(0);
        assertThat(stats2024.data().system()).isEqualTo(0);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getErrorsStatistics should return statistics sorted by year in descending order")
    void getErrorsStatistics_ShouldReturnStatisticsSortedDescending() {
        // given
        List<Integer> years = List.of(2023, 2024, 2022);

        // Mock port responses
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2022)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2022)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2022)).thenReturn(1);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2023)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2023)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2023)).thenReturn(2);

        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2024)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2024)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2024)).thenReturn(3);

        // when
        List<ErrorsStatistics> result = service.getErrorsStatistics(years, null, null);

        // then
        assertThat(result).hasSize(3);
        assertThat(result.get(0).year()).isEqualTo(2024); // First item should be most recent year
        assertThat(result.get(1).year()).isEqualTo(2023);
        assertThat(result.get(2).year()).isEqualTo(2022); // Last item should be oldest year
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getErrorsStatistics should filter by federal state and office when provided")
    void getErrorsStatistics_ShouldFilterByFederalStateAndOffice() {
        // given
        List<Integer> years = List.of(2023);
        String federalState = "BY";
        String office = "Office1";

        // Mock port responses for filtered data
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(3);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYearAndFederalStateAndOffice(2023, federalState, office)).thenReturn(1);

        // when
        List<ErrorsStatistics> result = service.getErrorsStatistics(years, federalState, office);

        // then
        assertThat(result).hasSize(1);

        ErrorsStatistics stats = result.get(0);
        assertThat(stats.year()).isEqualTo(2023);
        assertThat(stats.data().onlineService()).isEqualTo(2);
        assertThat(stats.data().cardPrinterService()).isEqualTo(3);
        assertThat(stats.data().system()).isEqualTo(1);
    }

    @Test
    @DisplayName("ErrorsStatisticsServiceImpl.getErrorsStatistics should use all available years when years list is empty")
    void getErrorsStatistics_ShouldUseAllAvailableYearsWhenYearsListIsEmpty() {
        // given
        List<Integer> emptyYears = List.of();
        List<Integer> availableYears = List.of(2023, 2024);

        when(errorsProtocolStatisticsPort.getAvailableYears()).thenReturn(availableYears);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2023)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2023)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2023)).thenReturn(1);
        when(errorsProtocolStatisticsPort.getOnlineServiceErrorsAmountByYear(2024)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getCardOrderErrorsAmountByYear(2024)).thenReturn(2);
        when(errorsProtocolStatisticsPort.getSystemErrorsAmountByYear(2024)).thenReturn(2);

        // when
        List<ErrorsStatistics> result = service.getErrorsStatistics(emptyYears, null, null);

        // then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).year()).isEqualTo(2024);
        assertThat(result.get(1).year()).isEqualTo(2023);
    }
}