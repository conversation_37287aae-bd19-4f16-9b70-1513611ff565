package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.registerservice.export.ExportContent;
import de.adesso.fischereiregister.registerservice.export.ExportContentType;
import de.adesso.fischereiregister.view.ban.services.BanViewService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BansStatisticsExportServiceImplTest {

    @Mock
    private BanViewService banViewService;

    private BansStatisticsExportServiceImpl exportService;

    @BeforeEach
    void setUp() {
        exportService = new BansStatisticsExportServiceImpl(banViewService);
    }

    @Test
    @DisplayName("exportBansStatistics should export CSV with federal state filter")
    void exportBansStatistics_WithFederalState_ShouldExportCsv() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2023, 2024);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(5);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(3);
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(8);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(6);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("bans-statistics-sh-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Neu verhängte Sperren,Gestartete Sperren");
        assertThat(csvContent).contains("2023,SH,5,3");
        assertThat(csvContent).contains("2024,SH,8,6");

        verify(banViewService).getIssuedAmountByFederalStateAndYear(federalState, 2023);
        verify(banViewService).getStartedAmountByFederalStateAndYear(federalState, 2023);
        verify(banViewService).getIssuedAmountByFederalStateAndYear(federalState, 2024);
        verify(banViewService).getStartedAmountByFederalStateAndYear(federalState, 2024);
        verifyNoMoreInteractions(banViewService);
    }

    @Test
    @DisplayName("exportBansStatistics should export CSV for all federal states when no filter")
    void exportBansStatistics_WithoutFederalState_ShouldExportAllStates() {
        // given
        String federalState = null;
        List<Integer> years = List.of(2023);

        // Mock responses for all federal states (just a few for testing)
        when(banViewService.getIssuedAmountByFederalStateAndYear("BW", 2023)).thenReturn(1);
        when(banViewService.getStartedAmountByFederalStateAndYear("BW", 2023)).thenReturn(1);
        when(banViewService.getIssuedAmountByFederalStateAndYear("BY", 2023)).thenReturn(2);
        when(banViewService.getStartedAmountByFederalStateAndYear("BY", 2023)).thenReturn(2);
        when(banViewService.getIssuedAmountByFederalStateAndYear("BE", 2023)).thenReturn(3);
        when(banViewService.getStartedAmountByFederalStateAndYear("BE", 2023)).thenReturn(3);
        when(banViewService.getIssuedAmountByFederalStateAndYear("BB", 2023)).thenReturn(4);
        when(banViewService.getStartedAmountByFederalStateAndYear("BB", 2023)).thenReturn(4);
        when(banViewService.getIssuedAmountByFederalStateAndYear("HB", 2023)).thenReturn(5);
        when(banViewService.getStartedAmountByFederalStateAndYear("HB", 2023)).thenReturn(5);
        when(banViewService.getIssuedAmountByFederalStateAndYear("HH", 2023)).thenReturn(6);
        when(banViewService.getStartedAmountByFederalStateAndYear("HH", 2023)).thenReturn(6);
        when(banViewService.getIssuedAmountByFederalStateAndYear("HE", 2023)).thenReturn(7);
        when(banViewService.getStartedAmountByFederalStateAndYear("HE", 2023)).thenReturn(7);
        when(banViewService.getIssuedAmountByFederalStateAndYear("MV", 2023)).thenReturn(8);
        when(banViewService.getStartedAmountByFederalStateAndYear("MV", 2023)).thenReturn(8);
        when(banViewService.getIssuedAmountByFederalStateAndYear("NI", 2023)).thenReturn(9);
        when(banViewService.getStartedAmountByFederalStateAndYear("NI", 2023)).thenReturn(9);
        when(banViewService.getIssuedAmountByFederalStateAndYear("NW", 2023)).thenReturn(10);
        when(banViewService.getStartedAmountByFederalStateAndYear("NW", 2023)).thenReturn(10);
        when(banViewService.getIssuedAmountByFederalStateAndYear("RP", 2023)).thenReturn(11);
        when(banViewService.getStartedAmountByFederalStateAndYear("RP", 2023)).thenReturn(11);
        when(banViewService.getIssuedAmountByFederalStateAndYear("SL", 2023)).thenReturn(12);
        when(banViewService.getStartedAmountByFederalStateAndYear("SL", 2023)).thenReturn(12);
        when(banViewService.getIssuedAmountByFederalStateAndYear("SN", 2023)).thenReturn(13);
        when(banViewService.getStartedAmountByFederalStateAndYear("SN", 2023)).thenReturn(13);
        when(banViewService.getIssuedAmountByFederalStateAndYear("ST", 2023)).thenReturn(14);
        when(banViewService.getStartedAmountByFederalStateAndYear("ST", 2023)).thenReturn(14);
        when(banViewService.getIssuedAmountByFederalStateAndYear("SH", 2023)).thenReturn(15);
        when(banViewService.getStartedAmountByFederalStateAndYear("SH", 2023)).thenReturn(15);
        when(banViewService.getIssuedAmountByFederalStateAndYear("TH", 2023)).thenReturn(16);
        when(banViewService.getStartedAmountByFederalStateAndYear("TH", 2023)).thenReturn(16);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        assertThat(result).isNotNull();
        assertThat(result.type()).isEqualTo(ExportContentType.CSV);
        assertThat(result.getFullFilename()).startsWith("bans-statistics-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("Jahr,Bundesland,Neu verhängte Sperren,Gestartete Sperren");
        assertThat(csvContent).contains("2023,BW,1,1");
        assertThat(csvContent).contains("2023,BY,2,2");
        assertThat(csvContent).contains("2023,SH,15,15");

        // Verify that all federal states were queried
        verify(banViewService, times(16)).getIssuedAmountByFederalStateAndYear(anyString(), eq(2023));
        verify(banViewService, times(16)).getStartedAmountByFederalStateAndYear(anyString(), eq(2023));
    }

    @Test
    @DisplayName("exportBansStatistics should handle empty federal state string")
    void exportBansStatistics_WithEmptyFederalState_ShouldExportAllStates() {
        // given
        String federalState = "";
        List<Integer> years = List.of(2023);

        // Mock responses for all federal states (just a few for testing)
        when(banViewService.getIssuedAmountByFederalStateAndYear(anyString(), eq(2023))).thenReturn(1);
        when(banViewService.getStartedAmountByFederalStateAndYear(anyString(), eq(2023))).thenReturn(1);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        assertThat(result).isNotNull();
        assertThat(result.getFullFilename()).startsWith("bans-statistics-");
        assertThat(result.getFullFilename()).endsWith(".csv");

        // Verify that all federal states were queried (16 federal states)
        verify(banViewService, times(16)).getIssuedAmountByFederalStateAndYear(anyString(), eq(2023));
        verify(banViewService, times(16)).getStartedAmountByFederalStateAndYear(anyString(), eq(2023));
    }

    @Test
    @DisplayName("exportBansStatistics should handle multiple years")
    void exportBansStatistics_WithMultipleYears_ShouldExportAllYears() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2022, 2023, 2024);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2022)).thenReturn(1);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2022)).thenReturn(1);
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(2);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(2);
        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(3);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2024)).thenReturn(3);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        String csvContent = new String(result.content(), StandardCharsets.UTF_8);
        assertThat(csvContent).contains("2022,SH,1,1");
        assertThat(csvContent).contains("2023,SH,2,2");
        assertThat(csvContent).contains("2024,SH,3,3");

        verify(banViewService).getIssuedAmountByFederalStateAndYear(federalState, 2022);
        verify(banViewService).getIssuedAmountByFederalStateAndYear(federalState, 2023);
        verify(banViewService).getIssuedAmountByFederalStateAndYear(federalState, 2024);
    }

    @Test
    @DisplayName("exportBansStatistics should handle exceptions")
    void exportBansStatistics_ShouldHandleExceptions() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2023);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023))
                .thenThrow(new RuntimeException("Database error"));

        // when & then
        assertThatThrownBy(() -> exportService.exportBansStatistics(federalState, years))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to export bans statistics");
    }

    @Test
    @DisplayName("exportBansStatistics should generate correct filename with federal state")
    void exportBansStatistics_ShouldGenerateCorrectFilenameWithFederalState() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2023);

        when(banViewService.getIssuedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(1);
        when(banViewService.getStartedAmountByFederalStateAndYear(federalState, 2023)).thenReturn(1);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        assertThat(result.getFullFilename()).matches("bans-statistics-sh-\\d{8}-\\d{6}\\.csv");
    }

    @Test
    @DisplayName("exportBansStatistics should generate correct filename without federal state")
    void exportBansStatistics_ShouldGenerateCorrectFilenameWithoutFederalState() {
        // given
        String federalState = null;
        List<Integer> years = List.of(2023);

        when(banViewService.getIssuedAmountByFederalStateAndYear(anyString(), eq(2023))).thenReturn(1);
        when(banViewService.getStartedAmountByFederalStateAndYear(anyString(), eq(2023))).thenReturn(1);

        // when
        ExportContent result = exportService.exportBansStatistics(federalState, years);

        // then
        assertThat(result.getFullFilename()).matches("bans-statistics-\\d{8}-\\d{6}\\.csv");
    }
}
